
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { useSanityPortfolio } from "@/components/SanityPortfolioProvider";
import { Button } from "@/components/ui/button";
import BasicHeader from "@/components/layout/BasicHeader";
import Hero from "@/components/sections/Hero";
import Features from "@/components/sections/Features";
import Stats from "@/components/sections/Stats";
import Testimonials from "@/components/sections/Testimonials";
import Technologies from "@/components/sections/Technologies";
import CTA from "@/components/sections/CTA";
import Footer from "@/components/layout/Footer";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { urlFor } from "@/lib/sanity";

const Index = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();
  const { portfolioItems, loading, error } = useSanityPortfolio();



  const steps = [
    {
      id: "discovery",
      number: "01",
      title: "Discovery & Planning",
      description: "We start by understanding your business goals, target audience, and requirements to create a detailed project roadmap."
    },
    {
      id: "design",
      number: "02",
      title: "Design & Prototyping",
      description: "Our designers create wireframes and interactive prototypes, focusing on user experience, brand consistency, and visual appeal."
    },
    {
      id: "development",
      number: "03",
      title: "Development",
      description: "Our developers bring the designs to life using modern technologies and frameworks, ensuring clean, efficient, and maintainable code."
    },
    {
      id: "testing",
      number: "04",
      title: "Testing & Launch",
      description: "We thoroughly test your website across devices and browsers before deploying it to production with minimal downtime."
    },
    {
      id: "support",
      number: "05",
      title: "Maintenance & Support",
      description: "We provide ongoing support, regular updates, and performance monitoring to ensure your website remains secure and effective."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <BasicHeader />
      <main className="flex-grow">
        <Hero />
        <Features />

        {/* How It Works Section */}
        <section className="py-16 md:py-24">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1 mb-4">
                {t('home.howItWorks.subtitle', 'Our Process')}
              </span>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                {t('home.howItWorks.title', 'Our Web Development Process')}
              </h2>
              <p className="text-lg text-muted-foreground">
                {t('home.howItWorks.description', 'We follow a structured, collaborative approach to deliver exceptional websites that meet your business objectives and exceed user expectations.')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 md:gap-8">
              {steps.map((step, index) => (
                <div key={step.id} className="relative p-6 md:p-8 bg-card rounded-xl border border-border hover:shadow-md transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className={cn(
                    "absolute -top-5 w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white font-bold",
                    direction === 'rtl' ? 'right-8' : 'left-8'
                  )}>
                    {step.number}
                  </div>
                  <h3 className="text-xl font-bold mt-4 mb-4 text-foreground">
                    {t(`home.howItWorks.steps.${step.id}.title`, step.title)}
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    {t(`home.howItWorks.steps.${step.id}.description`, step.description)}
                  </p>
                  <Link to="/services" className={cn(
                    "text-primary font-medium inline-flex items-center hover:underline",
                    direction === 'rtl' ? 'flex-row-reverse' : 'flex-row'
                  )}>
                    {t('common.learnMore', 'Learn more')}
                    <ArrowRight className={cn("h-4 w-4", direction === 'rtl' ? 'mr-1 rotate-180' : 'ml-1')} />
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>

        <Technologies />

        {/* Recent Projects Section */}
        <section className="py-16 md:py-24 bg-muted/30 dark:bg-muted/10">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1 mb-4">
                {t('home.caseStudies.subtitle', 'Our Portfolio')}
              </span>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                {t('home.caseStudies.title', 'Recent Projects')}
              </h2>
              <p className="text-lg text-muted-foreground">
                {t('home.caseStudies.description', "Explore our latest web development projects and see how we've helped businesses achieve their digital goals.")}
              </p>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading recent projects...</p>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  {t('home.caseStudies.error', 'Unable to load recent projects. Please try again later.')}
                </p>
              </div>
            ) : portfolioItems?.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {portfolioItems
                  .filter(project => project._id && !project.id) // Only show Sanity projects (have _id but no local id)
                  .sort((a, b) => {
                    // Enhanced sorting: prioritize year, then publishedAt, then fallback to current year for new items
                    const aDate = a.year ||
                      (a.publishedAt ? new Date(a.publishedAt).getFullYear() :
                        new Date().getFullYear());
                    const bDate = b.year ||
                      (b.publishedAt ? new Date(b.publishedAt).getFullYear() :
                        new Date().getFullYear());
                    return bDate - aDate;
                  })
                  .slice(0, 3)
                  .map((project) => (
                    <div key={project.id || project._id} className="bg-card rounded-xl overflow-hidden border border-border hover:shadow-lg transition-all duration-300 group h-full flex flex-col">
                      <div className="relative overflow-hidden aspect-[16/9]">
                        {/* Browser-like frame for website screenshots */}
                        <div className="absolute top-0 left-0 right-0 h-6 bg-muted/90 border-b border-border z-10 flex items-center px-2">
                          <div className="flex gap-1">
                            <div className="w-2 h-2 rounded-full bg-red-400"></div>
                            <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                            <div className="w-2 h-2 rounded-full bg-green-400"></div>
                          </div>
                          {project.website ? (
                            <div className="ml-2 text-xs text-muted-foreground truncate max-w-[150px]">
                              {project.website.replace(/^https?:\/\/(www\.)?/, '')}
                            </div>
                          ) : (
                            <div className="ml-2 text-xs text-muted-foreground/70 italic truncate max-w-[150px]">
                              {t('portfolio.noWebsite', 'Internal Project')}
                            </div>
                          )}
                        </div>

                        {/* Handle both local images and Sanity images */}
                        {project.image ? (
                          <div className="pt-6 h-full">
                            <img
                              src={project.image}
                              alt={project.title}
                              className="w-full h-full object-contain bg-white group-hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                        ) : project.mainImage ? (
                          <div className="pt-6 h-full">
                            <img
                              src={urlFor(project.mainImage).width(1600).height(900).url()}
                              alt={project.title}
                              className="w-full h-full object-contain bg-white group-hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                        ) : (
                          <div className="w-full h-full pt-6 bg-muted/30 flex items-center justify-center">
                            <div className="flex flex-col items-center">
                              <span className="text-3xl mb-2">🖥️</span>
                              <span className="text-muted-foreground text-sm">No image available</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="p-6 flex flex-col flex-grow">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="text-xl font-bold text-foreground">
                            {project.title}
                          </h3>
                          <span className="text-sm text-muted-foreground">
                            {project.year || (project.publishedAt ? new Date(project.publishedAt).getFullYear() : '')}
                          </span>
                        </div>
                        <p className="text-muted-foreground mb-4 line-clamp-3 flex-grow">
                          {project.description}
                        </p>
                        <div className="mt-auto">
                          <Link to={`/portfolio/${project.id || project.slug?.current || project._id}`}>
                            <Button
                              variant="link"
                              className={cn(
                                "p-0 h-auto text-primary",
                                direction === 'rtl' ? 'flex flex-row-reverse' : 'flex'
                              )}
                            >
                              {t('common.viewProject', 'View project')}
                              <ArrowRight className={cn("h-4 w-4", direction === 'rtl' ? 'mr-1 rotate-180' : 'ml-1')} />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  {t('home.caseStudies.noProjects', 'No recent projects available.')}
                </p>
              </div>
            )}
          </div>
        </section>

        <Stats />
        <Testimonials />
        <CTA />
      </main>
      <Footer />
    </div>
  );
};

export default Index;

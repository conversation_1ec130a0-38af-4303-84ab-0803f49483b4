@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Montserrat:wght@500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 96%;
    /* #F4F4F4 */
    --foreground: 0 0% 20%;
    /* #333333 */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;

    --primary: 234 62% 27%;
    /* #1A1F71 Midnight Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 169 100% 37%;
    /* #00BFA6 Crescent Teal */
    --secondary-foreground: 0 0% 100%;

    --accent: 49 100% 50%;
    /* #FFD600 Solar Yellow */
    --accent-foreground: 0 0% 20%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 234 62% 27%;

    --header-background: 234 100% 13%;
    /* #001543 */

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 10%;
    --foreground: 0 0% 98%;

    --card: 0 0% 15%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;

    --primary: 234 62% 27%;
    /* #1A1F71 Midnight Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 169 100% 37%;
    /* #00BFA6 Crescent Teal */
    --secondary-foreground: 0 0% 100%;

    --accent: 49 100% 50%;
    /* #FFD600 Solar Yellow */
    --accent-foreground: 0 0% 20%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 234 62% 27%;

    --header-background: 234 100% 13%;
    /* #001543 */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-display;
  }

  /* RTL Support */
  [dir="rtl"] .space-x-2> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-3> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-4> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-5> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-6> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-8> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }
}

.container {
  @apply px-4 md:px-6 max-w-7xl mx-auto;
}

/* Line clamp utilities */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Loading Spinner Animations */
@keyframes logo-glow {

  0%,
  100% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.3));
  }

  50% {
    filter: drop-shadow(0 0 16px rgba(139, 92, 246, 0.5));
  }
}

@keyframes orbit-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-scale {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes bounce-gentle {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-8px);
  }
}

@keyframes loading-progress {
  0% {
    width: 0%;
    opacity: 0.5;
  }

  50% {
    width: 70%;
    opacity: 1;
  }

  100% {
    width: 100%;
    opacity: 0.5;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes for loading animations */
.animate-logo-glow {
  animation: logo-glow 2s ease-in-out infinite;
}

.animate-orbit-spin {
  animation: orbit-spin 3s linear infinite;
}

.animate-pulse-scale {
  animation: pulse-scale 2s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 1.5s ease-in-out infinite;
}

.animate-loading-progress {
  animation: loading-progress 2s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}
import { useState } from "react";
import { LogoSpinner } from "./ui/logo-spinner";
import { LoadingState } from "./ui/loading-state";
import { LoadingOverlay, useLoadingOverlay } from "./ui/loading-overlay";
import { Button } from "./ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { cn } from "@/lib/utils";

export function LoadingDemo() {
  const [selectedVariant, setSelectedVariant] = useState<"default" | "pulse" | "orbit" | "glow" | "bounce">("glow");
  const [selectedSize, setSelectedSize] = useState<"sm" | "md" | "lg" | "xl">("md");
  const { isLoading, showLoading, hideLoading } = useLoadingOverlay();

  const variants = [
    { value: "default", label: "Default", description: "Classic spinning border" },
    { value: "pulse", label: "Pulse", description: "Pulsing circles effect" },
    { value: "orbit", label: "Orbit", description: "Orbiting dots animation" },
    { value: "glow", label: "Glow", description: "Glowing ring effect" },
    { value: "bounce", label: "Bounce", description: "Bouncing elements" }
  ] as const;

  const sizes = [
    { value: "sm", label: "Small" },
    { value: "md", label: "Medium" },
    { value: "lg", label: "Large" },
    { value: "xl", label: "Extra Large" }
  ] as const;

  const handleShowOverlay = () => {
    showLoading("Processing your request...");
    setTimeout(() => {
      hideLoading();
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            CodeSafir Loading Spinners
          </h1>
          <p className="text-muted-foreground text-lg">
            Modern, professional, and creative loading animations featuring your logo
          </p>
        </div>

        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Customization Controls</CardTitle>
            <CardDescription>
              Adjust the spinner appearance and behavior
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Variant Selection */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Animation Variant</label>
              <div className="flex flex-wrap gap-2">
                {variants.map((variant) => (
                  <Button
                    key={variant.value}
                    variant={selectedVariant === variant.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedVariant(variant.value)}
                    className="flex flex-col h-auto p-3"
                  >
                    <span className="font-medium">{variant.label}</span>
                    <span className="text-xs text-muted-foreground">{variant.description}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Size Selection */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Size</label>
              <div className="flex gap-2">
                {sizes.map((size) => (
                  <Button
                    key={size.value}
                    variant={selectedSize === size.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedSize(size.value)}
                  >
                    {size.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Overlay Demo */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Full Screen Overlay</label>
              <Button onClick={handleShowOverlay} variant="secondary">
                Show Loading Overlay (3s)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Live Preview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Logo Spinner Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Logo Spinner
                <Badge variant="secondary">{selectedVariant}</Badge>
              </CardTitle>
              <CardDescription>
                Standalone logo spinner component
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center min-h-[200px] bg-muted/20 rounded-lg">
                <LogoSpinner
                  variant={selectedVariant}
                  size={selectedSize}
                  text="Loading amazing content..."
                  showText={true}
                  speed="normal"
                />
              </div>
            </CardContent>
          </Card>

          {/* Loading State Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Loading State Component</CardTitle>
              <CardDescription>
                Full loading state with background and styling
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LoadingState
                message="Loading your dashboard..."
                size={selectedSize}
                variant="logo"
                spinnerVariant={selectedVariant}
                className="min-h-[200px]"
              />
            </CardContent>
          </Card>
        </div>

        {/* All Variants Showcase */}
        <Card>
          <CardHeader>
            <CardTitle>All Animation Variants</CardTitle>
            <CardDescription>
              Preview of all available animation styles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
              {variants.map((variant) => (
                <div key={variant.value} className="text-center space-y-3">
                  <div className="flex items-center justify-center min-h-[120px] bg-muted/20 rounded-lg">
                    <LogoSpinner
                      variant={variant.value}
                      size="md"
                      showText={false}
                      speed="normal"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium">{variant.label}</h3>
                    <p className="text-xs text-muted-foreground">{variant.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Usage Examples */}
        <Card>
          <CardHeader>
            <CardTitle>Usage Examples</CardTitle>
            <CardDescription>
              Code examples for implementing the loading spinners
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Basic Logo Spinner</h4>
              <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                <code>{`<LogoSpinner 
  variant="glow" 
  size="md" 
  text="Loading..." 
  showText={true} 
/>`}</code>
              </pre>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Loading State Component</h4>
              <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                <code>{`<LoadingState
  message="Loading content..."
  variant="logo"
  spinnerVariant="glow"
  size="md"
/>`}</code>
              </pre>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Full Screen Overlay</h4>
              <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                <code>{`const { isLoading, showLoading, hideLoading } = useLoadingOverlay();

// Show overlay
showLoading("Processing...");

// Hide overlay
hideLoading();

// Component
<LoadingOverlay 
  isVisible={isLoading} 
  variant="glow" 
  size="lg" 
/>`}</code>
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isLoading}
        variant="glow"
        size="lg"
        message="Processing your request..."
      />
    </div>
  );
}

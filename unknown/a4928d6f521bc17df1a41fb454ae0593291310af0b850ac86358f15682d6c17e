import { LucideIcon } from "lucide-react";

export interface Product {
  id: string;
  title: string;
  description: string;
  shortDescription: string;
  icon: LucideIcon;
  category: ProductCategory;
  features: string[];
  technologies: string[];
  pricing: ProductPricing;
  image?: string;
  gallery?: string[];
  testimonial?: ProductTestimonial;
  caseStudy?: ProductCaseStudy;
  popular?: boolean;
  featured?: boolean;
}

export interface ProductCategory {
  id: string;
  name: string;
  description: string;
  icon: LucideIcon;
}

export interface ProductPricing {
  type: 'fixed' | 'starting' | 'custom' | 'contact';
  amount?: number;
  currency?: string;
  period?: string;
  note?: string;
}

export interface ProductTestimonial {
  client: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
}

export interface ProductCaseStudy {
  title: string;
  description: string;
  challenge: string;
  solution: string;
  results: string[];
  metrics?: ProductMetric[];
}

export interface ProductMetric {
  label: string;
  value: string;
  improvement?: string;
}

export interface ProductFilter {
  category?: string;
  priceRange?: [number, number];
  technologies?: string[];
  featured?: boolean;
}

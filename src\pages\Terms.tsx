import { useTranslation } from "react-i18next";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";

const Terms = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <div className="min-h-screen bg-background">
      <BasicHeader />
      <main className="pt-40 mt-32">
        {/* Hero Section */}
        <section className="py-16 md:py-24">
          <div className="container">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                  Terms of Service
                </h1>
                <p className="text-xl text-muted-foreground">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
              </div>

              {/* Content */}
              <div className={cn(
                "prose prose-lg max-w-none",
                direction === 'rtl' ? 'prose-rtl' : '',
                "prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground"
              )}>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">1. Acceptance of Terms</h2>
                  <p className="mb-4 text-muted-foreground">
                    By accessing and using CodeSafir's services, you accept and agree to be bound by the
                    terms and provision of this agreement. If you do not agree to abide by the above,
                    please do not use this service.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">2. Services Description</h2>
                  <p className="mb-4 text-muted-foreground">
                    CodeSafir provides web development, design, and deployment services including but not limited to:
                  </p>
                  <ul className="list-disc pl-6 mb-4 text-muted-foreground">
                    <li>Custom web application development</li>
                    <li>Website design and development</li>
                    <li>E-commerce solutions</li>
                    <li>Content management systems</li>
                    <li>Technical consulting and support</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">3. User Responsibilities</h2>
                  <p className="mb-4 text-muted-foreground">
                    As a user of our services, you agree to:
                  </p>
                  <ul className="list-disc pl-6 mb-4 text-muted-foreground">
                    <li>Provide accurate and complete information</li>
                    <li>Maintain the confidentiality of your account credentials</li>
                    <li>Use our services in compliance with applicable laws</li>
                    <li>Not engage in any activity that could harm our systems or other users</li>
                    <li>Respect intellectual property rights</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">4. Payment Terms</h2>
                  <p className="mb-4 text-muted-foreground">
                    Payment terms will be specified in individual project agreements. Generally:
                  </p>
                  <ul className="list-disc pl-6 mb-4 text-muted-foreground">
                    <li>Payment schedules are outlined in project contracts</li>
                    <li>Late payments may incur additional fees</li>
                    <li>Refunds are subject to our refund policy</li>
                    <li>All prices are subject to applicable taxes</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">5. Intellectual Property</h2>
                  <p className="mb-4 text-muted-foreground">
                    Upon full payment, clients receive ownership of custom-developed code and content.
                    However, CodeSafir retains rights to:
                  </p>
                  <ul className="list-disc pl-6 mb-4 text-muted-foreground">
                    <li>General methodologies and know-how</li>
                    <li>Pre-existing intellectual property</li>
                    <li>Third-party components and libraries</li>
                    <li>Use project as portfolio example (with client consent)</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">6. Limitation of Liability</h2>
                  <p className="mb-4 text-muted-foreground">
                    CodeSafir shall not be liable for any indirect, incidental, special, consequential,
                    or punitive damages, including without limitation, loss of profits, data, use,
                    goodwill, or other intangible losses.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">7. Service Availability</h2>
                  <p className="mb-4 text-muted-foreground">
                    While we strive to maintain high availability, we do not guarantee uninterrupted
                    service. Scheduled maintenance and unforeseen circumstances may cause temporary
                    service interruptions.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">8. Termination</h2>
                  <p className="mb-4 text-muted-foreground">
                    Either party may terminate services with appropriate notice as specified in
                    individual contracts. Upon termination, all outstanding payments become due
                    immediately.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">9. Governing Law</h2>
                  <p className="mb-4 text-muted-foreground">
                    These terms shall be governed by and construed in accordance with the laws of Egypt,
                    without regard to its conflict of law provisions.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">10. Contact Information</h2>
                  <p className="mb-4 text-muted-foreground">
                    For questions about these Terms of Service, please contact us:
                  </p>
                  <div className="bg-muted/50 p-6 rounded-lg">
                    <p className="mb-2 text-foreground"><strong>Email:</strong> <EMAIL></p>
                    <p className="mb-2 text-foreground"><strong>Address:</strong> Alexandria, Egypt</p>
                    <p className="text-foreground"><strong>WhatsApp:</strong> +20 ************</p>
                  </div>
                </section>

                <section className="mb-8">
                  <h2 className="text-2xl font-semibold mb-4 text-foreground">11. Changes to Terms</h2>
                  <p className="text-muted-foreground">
                    We reserve the right to modify these terms at any time. Changes will be effective
                    immediately upon posting. Your continued use of our services constitutes acceptance
                    of the modified terms.
                  </p>
                </section>

              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Terms;

# CodeSafir Loading Spinners

Modern, professional, and creative loading animations featuring the CodeSafir logo.

## Overview

The loading spinner system consists of three main components:

1. **LogoSpinner** - Standalone logo spinner with multiple animation variants
2. **LoadingState** - Complete loading state component with background and styling
3. **LoadingOverlay** - Full-screen loading overlay with backdrop

## Components

### LogoSpinner

A versatile logo spinner component with multiple animation variants.

#### Props

```typescript
interface LogoSpinnerProps {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "pulse" | "orbit" | "glow" | "bounce";
  showText?: boolean;
  text?: string;
  speed?: "slow" | "normal" | "fast";
}
```

#### Usage

```tsx
import { LogoSpinner } from "@/components/ui/logo-spinner";

// Basic usage
<LogoSpinner />

// With custom variant and size
<LogoSpinner 
  variant="glow" 
  size="lg" 
  text="Loading amazing content..." 
  showText={true} 
/>

// Fast spinning orbit animation
<LogoSpinner 
  variant="orbit" 
  size="md" 
  speed="fast" 
  showText={false} 
/>
```

#### Animation Variants

1. **Default** - Classic spinning border with gradient
2. **Pulse** - Pulsing circles with scaling logo
3. **Orbit** - Orbiting dots with counter-rotating rings
4. **Glow** - Glowing ring with logo glow effect
5. **Bounce** - Bouncing dots with gentle logo bounce

### LoadingState

A complete loading state component with background, styling, and multiple variants.

#### Props

```typescript
interface LoadingStateProps {
  message?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "logo" | "simple";
  spinnerVariant?: "default" | "pulse" | "orbit" | "glow" | "bounce";
  fullScreen?: boolean;
}
```

#### Usage

```tsx
import { LoadingState } from "@/components/ui/loading-state";

// Logo variant (recommended)
<LoadingState
  message="Loading your dashboard..."
  variant="logo"
  spinnerVariant="glow"
  size="md"
/>

// Full screen loading
<LoadingState
  message="Processing..."
  variant="logo"
  spinnerVariant="orbit"
  fullScreen={true}
/>

// Simple spinner
<LoadingState
  message="Please wait..."
  variant="simple"
  size="sm"
/>
```

### LoadingOverlay

Full-screen loading overlay with backdrop blur and smooth animations.

#### Props

```typescript
interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  variant?: "default" | "pulse" | "orbit" | "glow" | "bounce";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  onComplete?: () => void;
  minDuration?: number; // Minimum duration in ms
}
```

#### Usage

```tsx
import { LoadingOverlay, useLoadingOverlay } from "@/components/ui/loading-overlay";

// Using the hook
function MyComponent() {
  const { isLoading, showLoading, hideLoading } = useLoadingOverlay();

  const handleAction = async () => {
    showLoading("Processing your request...");
    try {
      await someAsyncOperation();
    } finally {
      hideLoading();
    }
  };

  return (
    <>
      <button onClick={handleAction}>Start Process</button>
      <LoadingOverlay 
        isVisible={isLoading} 
        variant="glow" 
        size="lg" 
      />
    </>
  );
}

// Direct usage
<LoadingOverlay
  isVisible={true}
  message="Uploading files..."
  variant="orbit"
  size="xl"
  minDuration={1000}
  onComplete={() => console.log('Loading complete')}
/>
```

## Animation Details

### Custom CSS Animations

The loading spinners use custom CSS animations defined in `src/index.css`:

- `logo-glow` - Smooth glow effect for the logo
- `orbit-spin` - Smooth orbital rotation
- `pulse-scale` - Gentle scaling pulse
- `bounce-gentle` - Subtle bounce animation
- `loading-progress` - Progress bar animation
- `fade-in-up` - Entrance animation

### Performance Considerations

- All animations use CSS transforms and opacity for optimal performance
- GPU acceleration is enabled through `transform` properties
- Animations respect `prefers-reduced-motion` settings
- Minimal DOM manipulation during animations

## Integration Examples

### React Router Loading

```tsx
// In App.tsx
const RouteWrapper = ({ children, pageName }) => (
  <Suspense fallback={
    <LoadingState
      message={`Loading ${pageName}...`}
      variant="logo"
      spinnerVariant="glow"
      size="md"
    />
  }>
    {children}
  </Suspense>
);
```

### API Loading States

```tsx
function DataComponent() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await api.getData();
      setData(response);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <LoadingState
        message="Fetching latest data..."
        variant="logo"
        spinnerVariant="pulse"
      />
    );
  }

  return <div>{/* Your data content */}</div>;
}
```

### Form Submission

```tsx
function ContactForm() {
  const { isLoading, showLoading, hideLoading } = useLoadingOverlay();

  const handleSubmit = async (formData) => {
    showLoading("Sending your message...");
    try {
      await submitForm(formData);
      // Success handling
    } catch (error) {
      // Error handling
    } finally {
      hideLoading();
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        {/* Form fields */}
      </form>
      <LoadingOverlay 
        isVisible={isLoading} 
        variant="bounce" 
        size="lg" 
      />
    </>
  );
}
```

## Customization

### Theming

The spinners automatically adapt to your theme:
- Light/dark mode support through CSS variables
- Uses your brand colors (primary, secondary, accent)
- Respects your border radius settings

### Custom Colors

You can customize colors by overriding CSS variables or using Tailwind classes:

```tsx
<LogoSpinner 
  className="[&_.bg-blue-500]:bg-red-500 [&_.bg-purple-500]:bg-green-500"
  variant="orbit"
/>
```

### Custom Animations

Add your own animation variants by extending the component:

```tsx
// Custom variant
const CustomSpinner = ({ ...props }) => {
  return (
    <div className="relative animate-custom-spin">
      <LogoSpinner {...props} variant="default" />
    </div>
  );
};
```

## Best Practices

1. **Use appropriate sizes** - `sm` for inline loading, `lg` for page loading
2. **Choose meaningful messages** - Be specific about what's loading
3. **Respect user preferences** - Animations work with reduced motion settings
4. **Minimum duration** - Use `minDuration` to prevent flashing
5. **Consistent branding** - Use logo variants to maintain brand presence
6. **Performance** - Prefer CSS animations over JavaScript animations

## Accessibility

- All spinners include proper ARIA labels
- Screen reader announcements for loading states
- Respects `prefers-reduced-motion` settings
- Keyboard navigation support where applicable
- High contrast mode compatibility

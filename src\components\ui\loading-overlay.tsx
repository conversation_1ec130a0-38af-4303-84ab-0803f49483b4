import { cn } from "@/lib/utils";
import { LogoSpinner } from "./logo-spinner";
import { useEffect, useState } from "react";

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  variant?: "default" | "pulse" | "orbit" | "glow" | "bounce";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  onComplete?: () => void;
  minDuration?: number; // Minimum duration in ms to show the loader
}

export function LoadingOverlay({
  isVisible,
  message = "Loading...",
  variant = "glow",
  size = "lg",
  className,
  onComplete,
  minDuration = 500
}: LoadingOverlayProps) {
  const [shouldShow, setShouldShow] = useState(isVisible);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isVisible) {
      setShouldShow(true);
      setIsAnimating(true);
    } else if (shouldShow) {
      // Add a minimum duration before hiding
      timeoutId = setTimeout(() => {
        setIsAnimating(false);
        // Wait for fade out animation
        setTimeout(() => {
          setShouldShow(false);
          onComplete?.();
        }, 300);
      }, minDuration);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isVisible, shouldShow, minDuration, onComplete]);

  if (!shouldShow) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center",
        "bg-background/80 backdrop-blur-md",
        "transition-all duration-300 ease-in-out",
        isAnimating ? "opacity-100" : "opacity-0",
        className
      )}
      role="dialog"
      aria-modal="true"
      aria-labelledby="loading-message"
    >
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-blue-500/10" />
        <div
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)`
          }}
        />
      </div>

      {/* Loading content */}
      <div className={cn(
        "relative flex flex-col items-center justify-center p-8",
        "bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 shadow-2xl",
        "transform transition-all duration-500 ease-out",
        isAnimating ? "scale-100 translate-y-0" : "scale-95 translate-y-4"
      )}>
        {/* Decorative elements */}
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-blue-500/20 rounded-full animate-pulse" />
        <div className="absolute -top-1 -right-3 w-2 h-2 bg-purple-500/30 rounded-full animate-pulse delay-75" />
        <div className="absolute -bottom-2 -right-2 w-3 h-3 bg-green-500/20 rounded-full animate-pulse delay-150" />
        <div className="absolute -bottom-1 -left-3 w-2 h-2 bg-orange-500/30 rounded-full animate-pulse delay-300" />

        {/* Main spinner */}
        <LogoSpinner
          size={size}
          variant={variant}
          text={message}
          showText={true}
          speed="normal"
          className="mb-2"
        />

        {/* Progress indicator */}
        <div className="w-32 h-1 bg-muted rounded-full overflow-hidden mt-4">
          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-loading-progress" />
        </div>

        {/* Subtle hint text */}
        <p className="text-xs text-muted-foreground/60 mt-3 font-light">
          Crafting your experience...
        </p>
      </div>


    </div>
  );
}

// Hook for managing loading states
export function useLoadingOverlay(initialState = false) {
  const [isLoading, setIsLoading] = useState(initialState);
  const [loadingMessage, setLoadingMessage] = useState("Loading...");

  const showLoading = (message?: string) => {
    if (message) setLoadingMessage(message);
    setIsLoading(true);
  };

  const hideLoading = () => {
    setIsLoading(false);
  };

  const updateMessage = (message: string) => {
    setLoadingMessage(message);
  };

  return {
    isLoading,
    loadingMessage,
    showLoading,
    hideLoading,
    updateMessage
  };
}

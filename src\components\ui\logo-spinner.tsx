import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

interface LogoSpinnerProps {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "pulse" | "orbit" | "glow" | "bounce";
  showText?: boolean;
  text?: string;
  speed?: "slow" | "normal" | "fast";
}

export function LogoSpinner({
  className,
  size = "md",
  variant = "default",
  showText = true,
  text = "Loading...",
  speed = "normal"
}: LogoSpinnerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const sizeClasses = {
    sm: {
      container: "w-16 h-16",
      logo: "w-12 h-12",
      text: "text-xs mt-2",
      orbit: "w-20 h-20",
      dots: "w-1 h-1"
    },
    md: {
      container: "w-24 h-24",
      logo: "w-18 h-18",
      text: "text-sm mt-3",
      orbit: "w-32 h-32",
      dots: "w-1.5 h-1.5"
    },
    lg: {
      container: "w-32 h-32",
      logo: "w-24 h-24",
      text: "text-base mt-4",
      orbit: "w-40 h-40",
      dots: "w-2 h-2"
    },
    xl: {
      container: "w-40 h-40",
      logo: "w-32 h-32",
      text: "text-lg mt-5",
      orbit: "w-48 h-48",
      dots: "w-2.5 h-2.5"
    },
  };

  const speedClasses = {
    slow: "duration-3000",
    normal: "duration-2000",
    fast: "duration-1000"
  };

  const renderSpinner = () => {
    switch (variant) {
      case "pulse":
        return (
          <div className={cn("relative flex flex-col items-center justify-center", className)}>
            <div className={cn(
              "relative flex items-center justify-center",
              sizeClasses[size].container
            )}>
              {/* Pulsing background circles */}
              <div className={cn(
                "absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20",
                "animate-pulse"
              )} />
              <div className={cn(
                "absolute inset-2 rounded-full bg-gradient-to-r from-blue-400/30 to-purple-400/30",
                "animate-pulse delay-75"
              )} />
              <div className={cn(
                "absolute inset-4 rounded-full bg-gradient-to-r from-blue-300/40 to-purple-300/40",
                "animate-pulse delay-150"
              )} />

              {/* Logo */}
              <div className={cn(
                "relative z-10 transition-all duration-500 animate-pulse-scale",
                sizeClasses[size].logo,
                mounted ? "scale-100 opacity-100" : "scale-50 opacity-0"
              )}>
                <img
                  src="/logo.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain dark:hidden"
                />
                <img
                  src="/logo-dark.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain hidden dark:block"
                />
              </div>
            </div>

            {showText && (
              <p className={cn(
                "font-medium text-muted-foreground animate-pulse",
                sizeClasses[size].text
              )}>
                {text}
              </p>
            )}
          </div>
        );

      case "orbit":
        return (
          <div className={cn("relative flex flex-col items-center justify-center", className)}>
            <div className={cn(
              "relative flex items-center justify-center",
              sizeClasses[size].orbit
            )}>
              {/* Orbiting dots */}
              <div className="absolute inset-0 animate-orbit-spin">
                <div className={cn(
                  "absolute top-0 left-1/2 transform -translate-x-1/2 rounded-full bg-blue-500 animate-pulse",
                  sizeClasses[size].dots
                )} />
                <div className={cn(
                  "absolute top-1/2 right-0 transform -translate-y-1/2 rounded-full bg-purple-500 animate-pulse",
                  sizeClasses[size].dots
                )} style={{ animationDelay: '0.25s' }} />
                <div className={cn(
                  "absolute bottom-0 left-1/2 transform -translate-x-1/2 rounded-full bg-green-500 animate-pulse",
                  sizeClasses[size].dots
                )} style={{ animationDelay: '0.5s' }} />
                <div className={cn(
                  "absolute top-1/2 left-0 transform -translate-y-1/2 rounded-full bg-orange-500 animate-pulse",
                  sizeClasses[size].dots
                )} style={{ animationDelay: '0.75s' }} />
              </div>

              {/* Counter-rotating inner ring */}
              <div className="absolute inset-4 animate-orbit-spin" style={{ animationDirection: 'reverse', animationDuration: '2s' }}>
                <div className={cn(
                  "absolute top-0 left-1/2 transform -translate-x-1/2 rounded-full bg-blue-400/60 animate-pulse",
                  sizeClasses[size].dots
                )} />
                <div className={cn(
                  "absolute bottom-0 left-1/2 transform -translate-x-1/2 rounded-full bg-purple-400/60 animate-pulse",
                  sizeClasses[size].dots
                )} style={{ animationDelay: '0.5s' }} />
              </div>

              {/* Logo */}
              <div className={cn(
                "relative z-10 transition-all duration-500",
                sizeClasses[size].logo,
                mounted ? "scale-100 opacity-100" : "scale-50 opacity-0"
              )}>
                <img
                  src="/logo.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain dark:hidden"
                />
                <img
                  src="/logo-dark.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain hidden dark:block"
                />
              </div>
            </div>

            {showText && (
              <p className={cn(
                "font-medium text-muted-foreground",
                sizeClasses[size].text
              )}>
                {text}
              </p>
            )}
          </div>
        );

      case "glow":
        return (
          <div className={cn("relative flex flex-col items-center justify-center", className)}>
            <div className={cn(
              "relative flex items-center justify-center",
              sizeClasses[size].container
            )}>
              {/* Glowing ring */}
              <div className={cn(
                "absolute inset-0 rounded-full",
                "bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500",
                "animate-spin opacity-75",
                speedClasses[speed]
              )} style={{
                background: 'conic-gradient(from 0deg, #3b82f6, #8b5cf6, #3b82f6)',
                mask: 'radial-gradient(circle at center, transparent 65%, black 70%)',
                WebkitMask: 'radial-gradient(circle at center, transparent 65%, black 70%)'
              }} />

              {/* Inner glow */}
              <div className={cn(
                "absolute inset-2 rounded-full bg-gradient-to-r from-blue-400/30 to-purple-400/30",
                "animate-pulse"
              )} />

              {/* Logo */}
              <div className={cn(
                "relative z-10 transition-all duration-500 animate-logo-glow",
                sizeClasses[size].logo,
                mounted ? "scale-100 opacity-100" : "scale-50 opacity-0"
              )}>
                <img
                  src="/logo.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain dark:hidden"
                />
                <img
                  src="/logo-dark.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain hidden dark:block"
                />
              </div>
            </div>

            {showText && (
              <p className={cn(
                "font-medium text-muted-foreground",
                sizeClasses[size].text
              )}>
                {text}
              </p>
            )}
          </div>
        );

      case "bounce":
        return (
          <div className={cn("relative flex flex-col items-center justify-center", className)}>
            <div className={cn(
              "relative flex items-center justify-center",
              sizeClasses[size].container
            )}>
              {/* Bouncing dots around logo */}
              <div className="absolute inset-0">
                {[0, 1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className={cn(
                      "absolute rounded-full animate-bounce-gentle",
                      sizeClasses[size].dots,
                      i === 0 && "top-0 left-1/2 transform -translate-x-1/2 bg-blue-500",
                      i === 1 && "top-1/2 right-0 transform -translate-y-1/2 bg-purple-500",
                      i === 2 && "bottom-0 left-1/2 transform -translate-x-1/2 bg-green-500",
                      i === 3 && "top-1/2 left-0 transform -translate-y-1/2 bg-orange-500"
                    )}
                    style={{ animationDelay: `${i * 0.3}s` }}
                  />
                ))}
              </div>

              {/* Logo with gentle bounce */}
              <div className={cn(
                "relative z-10 transition-all duration-500 animate-bounce-gentle",
                sizeClasses[size].logo,
                mounted ? "scale-100 opacity-100" : "scale-50 opacity-0"
              )} style={{ animationDelay: '0.1s' }}>
                <img
                  src="/logo.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain dark:hidden"
                />
                <img
                  src="/logo-dark.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain hidden dark:block"
                />
              </div>
            </div>

            {showText && (
              <p className={cn(
                "font-medium text-muted-foreground animate-pulse",
                sizeClasses[size].text
              )}>
                {text}
              </p>
            )}
          </div>
        );

      default:
        return (
          <div className={cn("relative flex flex-col items-center justify-center", className)}>
            <div className={cn(
              "relative flex items-center justify-center",
              sizeClasses[size].container
            )}>
              {/* Spinning border */}
              <div className={cn(
                "absolute inset-0 rounded-full border-4 border-transparent",
                "bg-gradient-to-r from-blue-500 to-purple-500 animate-spin",
                speedClasses[speed]
              )} style={{
                background: 'conic-gradient(from 0deg, #3b82f6 0deg, #8b5cf6 180deg, #3b82f6 360deg)',
                mask: 'radial-gradient(circle at center, transparent 60%, black 65%)',
                WebkitMask: 'radial-gradient(circle at center, transparent 60%, black 65%)'
              }} />

              {/* Logo */}
              <div className={cn(
                "relative z-10 transition-all duration-500",
                sizeClasses[size].logo,
                mounted ? "scale-100 opacity-100" : "scale-50 opacity-0"
              )}>
                <img
                  src="/logo.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain dark:hidden"
                />
                <img
                  src="/logo-dark.png"
                  alt="CodeSafir"
                  className="w-full h-full object-contain hidden dark:block"
                />
              </div>
            </div>

            {showText && (
              <p className={cn(
                "font-medium text-muted-foreground",
                sizeClasses[size].text
              )}>
                {text}
              </p>
            )}
          </div>
        );
    }
  };

  return renderSpinner();
}


import { <PERSON><PERSON><PERSON>, ExternalLink, <PERSON>rkles, Code, Layers, Palette, Globe, Smartphone } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { CodeAnimation } from "@/components/CodeAnimation";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";

const Hero = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  const techBadges = [
    { icon: Code, label: "react" },
    { icon: Layers, label: "nodejs" },
    { icon: Palette, label: "uiux" },
    { icon: Globe, label: "webapps" },
    { icon: Smartphone, label: "responsive" }
  ];

  return (
    <section className="pt-40 pb-16 md:pt-48 md:pb-24 mt-32 overflow-hidden relative">
      {/* Background decorations */}
      <div className="absolute top-20 left-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute top-1/3 right-1/4 w-32 h-32 bg-accent/5 rounded-full blur-xl -z-10"></div>

      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <div className="inline-flex items-center gap-2 text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1.5 mb-6 animate-fade-in">
              <Sparkles className="h-3.5 w-3.5" />
              <span>{t('home.hero.agency')}</span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight animate-fade-in" style={{ animationDelay: '100ms' }}
              dangerouslySetInnerHTML={{
                __html: t('home.hero.mainTitle').replace('<em>', '<span class="text-primary">').replace('</em>', '</span>')
              }}
            />

            <p className="text-lg text-muted-foreground mb-8 max-w-md animate-fade-in" style={{ animationDelay: '200ms' }}>
              {t('home.hero.description')}
            </p>

            {/* Tech badges */}
            <div className={cn(
              "flex flex-wrap gap-2 mb-8 animate-fade-in",
              direction === 'rtl' ? 'justify-end' : 'justify-start'
            )} style={{ animationDelay: '250ms' }}>
              {techBadges.map((badge, index) => {
                const Icon = badge.icon;
                return (
                  <Badge
                    key={badge.label}
                    variant="outline"
                    className="bg-background/80 backdrop-blur-sm border-border/50 py-1.5 px-3 rounded-full"
                  >
                    <Icon className="h-3.5 w-3.5 mr-1.5 text-primary" />
                    <span>{t(`home.hero.techBadges.${badge.label}`)}</span>
                  </Badge>
                );
              })}
            </div>

            <div className={cn(
              "flex flex-col sm:flex-row gap-4 animate-fade-in",
              direction === 'rtl' ? 'sm:flex-row-reverse' : ''
            )} style={{ animationDelay: '300ms' }}>
              <Button
                size="lg"
                className="font-medium shadow-md hover:shadow-lg transition-shadow bg-primary hover:bg-primary/90"
                asChild
              >
                <Link to="/contact">
                  {t('common.getStarted')}
                  <ArrowRight className={cn("h-4 w-4", direction === 'rtl' ? 'mr-2' : 'ml-2')} />
                </Link>
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="font-medium hover:bg-secondary/10 transition-colors"
                asChild
              >
                <Link to="/services">
                  {t('nav.services')}
                  <ExternalLink className={cn("h-4 w-4", direction === 'rtl' ? 'mr-2' : 'ml-2')} />
                </Link>
              </Button>
            </div>

            <div className={cn(
              "mt-8 flex items-center animate-fade-in",
              direction === 'rtl' ? 'flex-row-reverse' : ''
            )} style={{ animationDelay: '400ms' }}>
              <div className={cn(
                "flex",
                direction === 'rtl' ? 'space-x-reverse -space-x-3 flex-row-reverse' : '-space-x-3'
              )}>
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={`client-avatar-${i}`}
                    className="w-10 h-10 rounded-full bg-gradient-to-br from-brand-400 to-brand-600 dark:from-brand-500 dark:to-brand-700 border-2 border-background flex items-center justify-center text-white text-xs font-medium shadow-sm"
                    aria-hidden="true"
                  >
                    {String.fromCharCode(64 + i)}
                  </div>
                ))}
              </div>
              <p className={cn(
                "text-sm text-muted-foreground",
                direction === 'rtl' ? 'mr-4' : 'ml-4'
              )}>
                <span className="font-medium">100+</span> {t('home.hero.clients', 'satisfied clients')}
              </p>
            </div>

            <div className={cn(
              "mt-4 flex items-center animate-fade-in",
              direction === 'rtl' ? 'flex-row-reverse' : ''
            )} style={{ animationDelay: '450ms' }}>
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={`rating-star-${star}`}
                    className="w-4 h-4 text-yellow-500 fill-current"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ))}
              </div>
              <p className={cn(
                "text-sm text-muted-foreground",
                direction === 'rtl' ? 'mr-4' : 'ml-4'
              )}>
                <span className="font-medium">4.9/5</span> {t('home.hero.rating', 'client satisfaction rating')}
              </p>
            </div>
          </div>

          <div className="relative order-first md:order-last">
            <div className="w-full aspect-square relative z-10">
              {/* Decorative elements */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/30 to-primary rounded-2xl shadow-xl transform rotate-3 scale-95 opacity-20 animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-tr from-secondary/30 to-secondary rounded-2xl shadow-xl animate-pulse"></div>
              <div className="absolute inset-0 bg-card rounded-2xl border border-border shadow-lg transform -rotate-2"></div>

              {/* Code animation */}
              <div className="absolute inset-0 transform -rotate-1 animate-fade-in" style={{ animationDelay: '500ms' }}>
                <CodeAnimation className="h-full" />
              </div>

              {/* Tech logos/icons floating around */}
              <div className="absolute -top-4 right-1/4 p-2 bg-white dark:bg-card rounded-lg shadow-lg border border-border/50 animate-float z-20">
                <Code className="h-6 w-6 text-primary" />
              </div>
              <div className="absolute top-1/3 -right-4 p-2 bg-white dark:bg-card rounded-lg shadow-lg border border-border/50 animate-float-delayed z-20">
                <Palette className="h-6 w-6 text-secondary" />
              </div>
              <div className="absolute -bottom-4 left-1/3 p-2 bg-white dark:bg-card rounded-lg shadow-lg border border-border/50 animate-float-slow z-20">
                <Globe className="h-6 w-6 text-primary" />
              </div>
              <div className="absolute bottom-1/4 -left-4 p-2 bg-white dark:bg-card rounded-lg shadow-lg border border-border/50 animate-float-delayed z-20">
                <Smartphone className="h-6 w-6 text-secondary" />
              </div>
            </div>

            {/* Floating decorations */}
            <div className="absolute -bottom-10 -right-10 w-24 h-24 bg-accent/20 rounded-full z-0 animate-pulse"></div>
            <div className="absolute -top-10 -left-10 w-16 h-16 bg-secondary/20 rounded-full z-0 animate-pulse"></div>

            {/* Additional decorative elements */}
            <div className="absolute top-1/2 -right-6 w-12 h-12 bg-primary/10 rounded-full z-0 animate-pulse" style={{ animationDuration: '3s' }}></div>
            <div className="absolute bottom-1/3 -left-8 w-8 h-8 bg-secondary/10 rounded-full z-0 animate-pulse" style={{ animationDuration: '4s' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;

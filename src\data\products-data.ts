import {
  Code,
  ShoppingCart,
  Database,
  Cloud,
  Laptop,
  Server,
  Palette,
  Gauge,
  Globe,
  Smartphone,
  Shield,
  Zap,
  Users,
  BarChart3,
  <PERSON>tings,
  Layers
} from "lucide-react";
import { Product, ProductCategory } from "@/types/products";

export const productCategories: ProductCategory[] = [
  {
    id: 'cms-systems',
    name: 'CMS Systems',
    description: 'Content Management Systems and platforms',
    icon: Database
  },
  {
    id: 'browser-extensions',
    name: 'Browser Extensions',
    description: 'Chrome extensions and browser tools',
    icon: Globe
  },
  {
    id: 'saas-products',
    name: 'SaaS Products',
    description: 'Software as a Service solutions',
    icon: Cloud
  },
  {
    id: 'web-tools',
    name: 'Web Tools',
    description: 'Online tools and utilities',
    icon: Settings
  },
  {
    id: 'mobile-apps',
    name: 'Mobile Apps',
    description: 'Mobile applications and tools',
    icon: Smartphone
  }
];

export const products: Product[] = [
  {
    id: 'codesafir-cms',
    title: 'CodeSafir CMS',
    shortDescription: 'Powerful headless CMS for modern websites',
    description: 'A modern, headless content management system built with React and Node.js, designed for developers and content creators who need flexibility and performance.',
    icon: Database,
    category: productCategories[0], // CMS Systems
    features: [
      'Headless architecture',
      'RESTful & GraphQL APIs',
      'Multi-language support',
      'Custom content types',
      'Media management',
      'User roles & permissions',
      'Real-time collaboration',
      'SEO optimization tools'
    ],
    technologies: ['React', 'Node.js', 'MongoDB', 'GraphQL', 'TypeScript', 'AWS'],
    pricing: {
      type: 'starting',
      amount: 99,
      currency: 'USD',
      period: 'month',
      note: 'Free tier available for small projects'
    },
    popular: true,
    featured: true,
    testimonial: {
      client: 'Ahmed Hassan',
      company: 'Digital Agency Pro',
      content: 'CodeSafir CMS has revolutionized how we manage content for our clients. The headless approach gives us incredible flexibility.',
      rating: 5
    }
  },
  {
    id: 'dev-tools-extension',
    title: 'DevTools Pro Extension',
    shortDescription: 'Chrome extension for web developers',
    description: 'A powerful Chrome extension that enhances developer productivity with advanced debugging tools, code snippets, and performance monitoring.',
    icon: Globe,
    category: productCategories[1], // Browser Extensions
    features: [
      'Advanced debugging tools',
      'Code snippet manager',
      'Performance monitoring',
      'API testing interface',
      'Local storage inspector',
      'CSS grid visualizer',
      'Accessibility checker',
      'Dark/Light theme support'
    ],
    technologies: ['JavaScript', 'Chrome APIs', 'HTML5', 'CSS3', 'Webpack'],
    pricing: {
      type: 'fixed',
      amount: 29,
      currency: 'USD',
      note: 'One-time purchase, lifetime updates'
    },
    popular: true,
    caseStudy: {
      title: 'Developer Productivity Boost',
      description: 'Helped 10,000+ developers improve their workflow efficiency',
      challenge: 'Developers needed better tools for debugging and testing',
      solution: 'Comprehensive Chrome extension with integrated dev tools',
      results: [
        '10,000+ active users',
        '4.8/5 Chrome Web Store rating',
        '40% faster debugging workflow'
      ]
    }
  },
  {
    id: 'project-manager-saas',
    title: 'ProjectFlow SaaS',
    shortDescription: 'Cloud-based project management platform',
    description: 'A comprehensive SaaS solution for project management, team collaboration, and workflow automation designed for modern teams.',
    icon: Cloud,
    category: productCategories[2], // SaaS Products
    features: [
      'Project planning & tracking',
      'Team collaboration tools',
      'Time tracking & reporting',
      'File sharing & storage',
      'Automated workflows',
      'Real-time notifications',
      'Custom dashboards',
      'API integrations'
    ],
    technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'AWS', 'Docker'],
    pricing: {
      type: 'starting',
      amount: 19,
      currency: 'USD',
      period: 'month',
      note: 'Per user pricing, 14-day free trial'
    },
    featured: true,
    testimonial: {
      client: 'Maria Rodriguez',
      company: 'Creative Studio LLC',
      content: 'ProjectFlow has transformed how our team collaborates. The automation features save us hours every week.',
      rating: 5
    }
  },
  {
    id: 'code-formatter-tool',
    title: 'CodeFormat Pro',
    shortDescription: 'Online code formatting and beautification tool',
    description: 'A powerful web-based tool for formatting, beautifying, and validating code in multiple programming languages with advanced customization options.',
    icon: Settings,
    category: productCategories[3], // Web Tools
    features: [
      'Multi-language support',
      'Custom formatting rules',
      'Syntax highlighting',
      'Error detection',
      'Batch processing',
      'API access',
      'Export options',
      'Team collaboration'
    ],
    technologies: ['React', 'Monaco Editor', 'Node.js', 'Prettier', 'ESLint'],
    pricing: {
      type: 'starting',
      amount: 9,
      currency: 'USD',
      period: 'month',
      note: 'Free tier with basic features available'
    },
    popular: true
  },
  {
    id: 'task-tracker-mobile',
    title: 'TaskTracker Mobile',
    shortDescription: 'Mobile task management app',
    description: 'A sleek mobile application for personal and team task management with offline sync, reminders, and productivity analytics.',
    icon: Smartphone,
    category: productCategories[4], // Mobile Apps
    features: [
      'Offline task management',
      'Smart reminders',
      'Team collaboration',
      'Progress tracking',
      'Time logging',
      'Custom categories',
      'Data synchronization',
      'Productivity insights'
    ],
    technologies: ['React Native', 'SQLite', 'Firebase', 'Push Notifications'],
    pricing: {
      type: 'fixed',
      amount: 4.99,
      currency: 'USD',
      note: 'One-time purchase, available on iOS and Android'
    }
  },
  {
    id: 'blog-cms-lite',
    title: 'BlogCMS Lite',
    shortDescription: 'Lightweight CMS for bloggers',
    description: 'A simple yet powerful content management system specifically designed for bloggers and content creators who need a fast, SEO-friendly platform.',
    icon: Database,
    category: productCategories[0], // CMS Systems
    features: [
      'Markdown editor',
      'SEO optimization',
      'Theme customization',
      'Comment management',
      'Social media integration',
      'Analytics dashboard',
      'Backup & restore',
      'Multi-author support'
    ],
    technologies: ['Next.js', 'MDX', 'Tailwind CSS', 'Vercel', 'GitHub'],
    pricing: {
      type: 'starting',
      amount: 15,
      currency: 'USD',
      period: 'month',
      note: 'Free plan available for personal blogs'
    },
    popular: true
  }
];

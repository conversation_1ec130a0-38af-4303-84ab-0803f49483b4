
import { ArrowRight, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/i18n/LanguageProvider";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

const CTA = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <section className="py-16 md:py-24 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-header-background via-header-background/90 to-header-background/80"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0yaDF2NGgtMXYtNHptMi0yaDF2MWgtMXYtMXptLTIgMmgxdjFoLTF2LTF6bS0yLTJoMXYxaC0xdi0xem0yLTJoMXYxaC0xVjI4eiIvPjwvZz48L2c+PC9zdmc+')] opacity-20"></div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-1/4 w-64 h-64 bg-yellow-400/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>

      <div className="container relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <span className="inline-block text-sm font-medium bg-white/20 text-white rounded-full px-3 py-1 mb-6">
            {t('home.cta.subtitle', 'Let\'s discuss how we can help you achieve your goals')}
          </span>

          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-white leading-tight">
            {t('home.cta.title', 'Ready to Transform Your Web Presence?')}
          </h2>

          <p className="text-lg text-white/90 mb-10 max-w-2xl mx-auto">
            {t('home.cta.description', 'Whether you need a brand new website, a redesign, or custom web application, our team is ready to bring your vision to life with cutting-edge technology and stunning design.')}
          </p>

          <div className={cn(
            "flex flex-col sm:flex-row justify-center gap-4",
            direction === 'rtl' ? 'sm:flex-row-reverse' : ''
          )}>
            <Button
              size="lg"
              className="font-medium bg-yellow-400 text-black hover:bg-yellow-500 shadow-lg hover:shadow-xl transition-all duration-200"
              asChild
            >
              <Link to="/contact">
                {t('home.cta.primaryButton', 'Start Your Project')}
                <ArrowRight className={cn("h-4 w-4", direction === 'rtl' ? 'mr-2' : 'ml-2')} />
              </Link>
            </Button>

            <Button
              size="lg"
              className="font-medium bg-white/10 text-white hover:bg-white/20 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-200"
              asChild
            >
              <Link to="/contact">
                <MessageSquare className={cn("h-4 w-4", direction === 'rtl' ? 'ml-2' : 'mr-2')} />
                {t('home.cta.secondaryButton', 'Schedule a Consultation')}
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-header-background/50 to-transparent"></div>
    </section>
  );
};

export default CTA;

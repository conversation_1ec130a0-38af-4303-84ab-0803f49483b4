import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ThemeToggle';
import { CodeSafirLogo } from '@/components/CodeSafirLogo';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { useLanguage } from '@/i18n/LanguageProvider';
import { cn } from '@/lib/utils';

const BasicHeader: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { direction } = useLanguage();
  // Set the document direction based on the language
  React.useEffect(() => {
    document.documentElement.dir = direction;
  }, [direction]);
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  const navItems = [
    { id: 'home', name: t('nav.home'), path: '/' },
    { id: 'about', name: t('nav.about'), path: '/about' },
    { id: 'services', name: t('nav.services'), path: '/services' },
    { id: 'products', name: t('nav.products'), path: '/products', comingSoon: true },
    { id: 'portfolio', name: t('nav.portfolio'), path: '/portfolio' },
    { id: 'contact', name: t('nav.contact'), path: '/contact' },
  ];

  return (
    <header className="bg-header-background shadow-lg backdrop-blur-sm fixed top-0 left-0 right-0 z-50 w-full text-white border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Logo - Centered at top */}
        <div className="relative flex justify-center py-3 border-b border-white/20">
          <Link to="/" className="transition-transform hover:scale-105 duration-200">
            <CodeSafirLogo size="md" />
          </Link>

          {/* Mobile menu button - Only visible on mobile, positioned in top right */}
          <div className="flex items-center md:hidden absolute right-0 top-1/2 transform -translate-y-1/2">
            <LanguageSwitcher variant="ghost" size="icon" showDropdown className="text-white hover:text-yellow-400 hover:bg-white/10 transition-colors" />
            <ThemeToggle className="mx-2 text-white hover:text-yellow-400 hover:bg-white/10 transition-colors" />
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-yellow-400 hover:bg-white/10 focus:outline-none"
              aria-controls="mobile-menu"
              aria-expanded="false"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <X className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* Navigation Row - Only visible on desktop */}
        <div className="relative hidden md:flex justify-center items-center h-12 py-2">
          {/* Desktop Navigation - Centered */}
          <div className="flex items-center space-x-8">
            {navItems.map((item) => {
              const isActive =
                (item.path === '/' && location.pathname === '/') ||
                (item.path !== '/' && location.pathname.startsWith(item.path));

              if (item.comingSoon) {
                return (
                  <div
                    key={item.id}
                    className="px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent relative cursor-not-allowed"
                  >
                    <span className="text-white/60">{item.name}</span>
                    <span className="absolute -top-2 -right-3 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-md z-10 whitespace-nowrap">
                      {t('common.comingSoon', 'Coming Soon')}
                    </span>
                  </div>
                );
              }

              return (
                <Link
                  key={item.id}
                  to={item.path}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border-b-2 ${isActive
                    ? 'text-yellow-400 font-semibold border-yellow-400 bg-white/5'
                    : 'text-white/90 hover:text-yellow-400 border-transparent hover:border-yellow-400/50 hover:bg-white/5'
                    }`}
                >
                  {item.name}
                </Link>
              );
            })}
          </div>

          {/* Desktop Actions - Positioned on the right */}
          <div className="hidden md:flex md:items-center md:space-x-4 md:absolute md:right-4">
            <LanguageSwitcher variant="ghost" showDropdown className="text-white hover:text-yellow-400 hover:bg-white/10 transition-colors" />
            <ThemeToggle className="text-white hover:text-yellow-400 hover:bg-white/10 transition-colors" />
            <Button size="sm" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold shadow-md hover:shadow-lg transition-all duration-200" asChild>
              <Link to="/contact">
                {t('common.getStarted')}
              </Link>
            </Button>
          </div>


        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      <div
        className={`md:hidden ${mobileMenuOpen ? 'block' : 'hidden'} bg-header-background shadow-lg border-t border-border/20`}
        id="mobile-menu"
      >
        <div className="px-4 pt-3 pb-4 space-y-2 sm:px-5">
          {navItems.map((item) => {
            const isActive =
              (item.path === '/' && location.pathname === '/') ||
              (item.path !== '/' && location.pathname.startsWith(item.path));

            if (item.comingSoon) {
              return (
                <div
                  key={item.id}
                  className={cn(
                    "block px-3 py-2 rounded-md text-base font-medium relative cursor-not-allowed",
                    direction === 'rtl' ? 'text-right' : 'text-left'
                  )}
                >
                  <span className="text-foreground/60">{item.name}</span>
                  <span className={cn(
                    "absolute top-1 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-md z-10 whitespace-nowrap",
                    direction === 'rtl' ? 'left-3' : 'right-3'
                  )}>
                    {t('common.comingSoon', 'Coming Soon')}
                  </span>
                </div>
              );
            }

            return (
              <Link
                key={item.id}
                to={item.path}
                className={cn(
                  "block px-3 py-2 rounded-md text-base font-medium",
                  isActive
                    ? 'text-yellow-400 font-semibold'
                    : 'text-white/90 hover:text-yellow-400',
                  direction === 'rtl' ? 'text-right' : 'text-left'
                )}
                onClick={() => setMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            );
          })}
          <div className="pt-4 mt-4 border-t border-white/20">
            <Button className="w-full mt-2 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold" asChild>
              <Link to="/contact" onClick={() => setMobileMenuOpen(false)}>
                {t('common.getStarted')}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default BasicHeader;

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/i18n/LanguageProvider';
import { Globe, Languages } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu";

interface LanguageSwitcherProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showDropdown?: boolean;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'outline',
  size = 'default',
  className = '',
  showDropdown = false
}) => {
  const { t } = useTranslation();
  const { language, direction, changeLanguage } = useLanguage();

  const toggleLanguage = () => {
    changeLanguage(language === 'en' ? 'ar' : 'en');
  };

  if (showDropdown) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size={size}
            className={cn(
              "rounded-full border border-white/20 hover:bg-white/10 transition-colors text-white hover:text-yellow-400 hover:border-yellow-400/50",
              size === 'icon' && "w-9 h-9",
              className
            )}
            aria-label={t('common.switchLanguage', 'Switch language')}
          >
            {size === 'icon' ? (
              <Languages className="h-4 w-4 text-white" />
            ) : (
              <>
                <Languages className={cn("h-4 w-4 text-white", direction === 'rtl' ? 'ml-2' : 'mr-2')} />
                {t('nav.language')}
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[180px]">
          <DropdownMenuItem
            onClick={() => changeLanguage('en')}
            className={cn("gap-2", language === "en" && "bg-accent")}
          >
            <span className="font-medium">English</span>
            {language === "en" && (
              <DropdownMenuShortcut>✓</DropdownMenuShortcut>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => changeLanguage('ar')}
            className={cn("gap-2", language === "ar" && "bg-accent")}
          >
            <span className="font-medium">العربية</span>
            {language === "ar" && (
              <DropdownMenuShortcut>✓</DropdownMenuShortcut>
            )}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleLanguage}
      className={cn(
        size === 'icon' && "rounded-full w-9 h-9",
        className
      )}
      aria-label={t('common.switchLanguage', 'Switch language')}
    >
      {size === 'icon' ? (
        <Languages className="h-4 w-4" />
      ) : (
        <>
          <Languages className={cn("h-4 w-4", direction === 'rtl' ? 'ml-2' : 'mr-2')} />
          {language === 'en' ? 'العربية' : 'English'}
        </>
      )}
    </Button>
  );
};

export default LanguageSwitcher;

import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ArrowR<PERSON>, Star, Check } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { Product } from "@/types/products";

interface ProductCardProps {
  product: Product;
  viewMode?: "grid" | "list";
  className?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  viewMode = "grid",
  className 
}) => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  const formatPrice = (product: Product) => {
    const { pricing } = product;
    
    switch (pricing.type) {
      case 'fixed':
        return `${pricing.currency}${pricing.amount?.toLocaleString()}`;
      case 'starting':
        return `${t('products.productCard.startingFrom')} ${pricing.currency}${pricing.amount?.toLocaleString()}`;
      case 'custom':
        return t('products.productCard.custom');
      case 'contact':
        return t('products.productCard.contact');
      default:
        return t('products.productCard.contact');
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (viewMode === "list") {
    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        transition={{ duration: 0.5 }}
        className={cn("w-full", className)}
      >
        <Card className="hover:shadow-lg transition-all duration-300 group">
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/3 p-6 flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5">
              <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <product.icon className="w-8 h-8 text-primary" />
              </div>
            </div>
            
            <div className="md:w-2/3 flex flex-col">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CardTitle className="text-xl group-hover:text-primary transition-colors">
                        {product.title}
                      </CardTitle>
                      {product.popular && (
                        <Badge variant="secondary" className="text-xs">
                          {t('products.productCard.popular')}
                        </Badge>
                      )}
                      {product.featured && (
                        <Badge className="text-xs">
                          {t('products.productCard.featured')}
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="text-sm">
                      {product.shortDescription}
                    </CardDescription>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-lg font-semibold text-primary">
                      {formatPrice(product)}
                    </div>
                    {product.pricing.note && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {product.pricing.note}
                      </p>
                    )}
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0 pb-4">
                <div className="space-y-3">
                  <div>
                    <h4 className="text-sm font-medium mb-2">{t('products.productCard.features')}</h4>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-1">
                      {product.features.slice(0, 4).map((feature, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Check className="w-3 h-3 text-secondary flex-shrink-0" />
                          <span className="truncate">{feature}</span>
                        </div>
                      ))}
                    </div>
                    {product.features.length > 4 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        +{product.features.length - 4} more features
                      </p>
                    )}
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">{t('products.productCard.technologies')}</h4>
                    <div className="flex flex-wrap gap-1">
                      {product.technologies.slice(0, 6).map((tech, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                      {product.technologies.length > 6 && (
                        <Badge variant="outline" className="text-xs">
                          +{product.technologies.length - 6}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="pt-0">
                <div className="flex gap-2 w-full">
                  <Button className="flex-1" size="sm">
                    {t('products.productCard.viewDetails')}
                    <ArrowRight className={cn(
                      "w-4 h-4 transition-transform group-hover:translate-x-1",
                      direction === 'rtl' ? 'mr-2 group-hover:-translate-x-1' : 'ml-2'
                    )} />
                  </Button>
                  <Button variant="outline" size="sm">
                    {t('products.productCard.getQuote')}
                  </Button>
                </div>
              </CardFooter>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  }

  // Grid view (default)
  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      transition={{ duration: 0.5 }}
      className={cn("w-full", className)}
    >
      <Card className="h-full hover:shadow-lg transition-all duration-300 group">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-primary/10 to-secondary/10 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <product.icon className="w-8 h-8 text-primary" />
          </div>
          
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2 flex-wrap">
              <CardTitle className="text-lg group-hover:text-primary transition-colors">
                {product.title}
              </CardTitle>
              {product.popular && (
                <Badge variant="secondary" className="text-xs">
                  {t('products.productCard.popular')}
                </Badge>
              )}
              {product.featured && (
                <Badge className="text-xs">
                  {t('products.productCard.featured')}
                </Badge>
              )}
            </div>
            
            <CardDescription className="text-sm">
              {product.shortDescription}
            </CardDescription>
            
            <div className="text-lg font-semibold text-primary">
              {formatPrice(product)}
            </div>
            {product.pricing.note && (
              <p className="text-xs text-muted-foreground">
                {product.pricing.note}
              </p>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">{t('products.productCard.features')}</h4>
            <div className="space-y-1">
              {product.features.slice(0, 4).map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Check className="w-3 h-3 text-secondary flex-shrink-0" />
                  <span className="truncate">{feature}</span>
                </div>
              ))}
            </div>
            {product.features.length > 4 && (
              <p className="text-xs text-muted-foreground mt-2">
                +{product.features.length - 4} more features
              </p>
            )}
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">{t('products.productCard.technologies')}</h4>
            <div className="flex flex-wrap gap-1">
              {product.technologies.slice(0, 4).map((tech, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tech}
                </Badge>
              ))}
              {product.technologies.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{product.technologies.length - 4}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>

        <CardFooter className="pt-0">
          <div className="flex flex-col gap-2 w-full">
            <Button className="w-full" size="sm">
              {t('products.productCard.viewDetails')}
              <ArrowRight className={cn(
                "w-4 h-4 transition-transform group-hover:translate-x-1",
                direction === 'rtl' ? 'mr-2 group-hover:-translate-x-1' : 'ml-2'
              )} />
            </Button>
            <Button variant="outline" size="sm" className="w-full">
              {t('products.productCard.getQuote')}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

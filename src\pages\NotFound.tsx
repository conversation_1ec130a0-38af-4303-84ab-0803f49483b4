
import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { useLanguage } from "@/i18n/LanguageProvider";

const NotFound = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();
  const location = useLocation();

  useEffect(() => {
    // Track 404 errors for analytics if needed
    // Could send to error tracking service here
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col">
      <BasicHeader />
      <main className="flex-grow flex items-center justify-center px-4 pt-40 mt-32">
        <div className="max-w-md w-full text-center">
          <div className="flex justify-center mb-6">
            <div className="p-3 bg-primary/10 dark:bg-primary/20 rounded-full">
              <AlertTriangle className="h-12 w-12 text-primary" />
            </div>
          </div>
          <h1 className="text-5xl font-bold text-foreground mb-4">404</h1>
          <p className="text-xl text-muted-foreground mb-8">
            {t('notFound.message')}
          </p>
          <Button asChild size="lg">
            <Link to="/">{t('notFound.returnHome')}</Link>
          </Button>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default NotFound;
